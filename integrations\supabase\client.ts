// Supabase Client Configuration
// يقرأ إعدادات Supabase من متغيرات البيئة
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// قراءة متغيرات البيئة مع التحقق من وجودها
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// التحقق من وجود المتغيرات المطلوبة
if (!SUPABASE_URL) {
  throw new Error(
    'متغير البيئة NEXT_PUBLIC_SUPABASE_URL مفقود. تأكد من إضافته في ملف .env.local'
  );
}

if (!SUPABASE_PUBLISHABLE_KEY) {
  throw new Error(
    'متغير البيئة NEXT_PUBLIC_SUPABASE_ANON_KEY مفقود. تأكد من إضافته في ملف .env.local'
  );
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
